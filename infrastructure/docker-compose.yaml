services:
  os01:
    container_name: os01
    image: opensearchproject/opensearch:2.19.2
    restart: unless-stopped
    environment:
      - bootstrap.memory_lock=true
      - DISABLE_SECURITY_PLUGIN=true
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx1g"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    cap_add:
      - IPC_LOCK
    volumes:
      - data01:/usr/share/opensearch/data
    ports:
      - 9200:9200
    networks:
      - stack

  osd01:
    restart: on-failure
    build:
      context: ./dockerfiles/OpenSearchDashboards
    container_name: osd01
    ports:
      - 5601:5601
    environment:
      - OPENSEARCH_URL=http://os01:9200
      - OPENSEARCH_HOSTS=http://os01:9200
    networks:
      - stack

  localstack:
    container_name: localstack
    restart: on-failure
    image: localstack/localstack:4.3
    privileged: true
    networks:
      - stack
    ports:
      - "4510-4580:4510-4580" # external service port range
      - "8080:8080" # LocalStack Web UI
    environment:
      - SERVICES=sqs,sns,lambda,sts,ecr,cloudformation,s3,iam,events,logs,apigateway,ssm
      - DEBUG=1
      - LAMBDA_IGNORE_ARCHITECTURE=1
    healthcheck:
      test: awslocal sns list-topics && awslocal sqs list-queues && awslocal lambda list-functions
      interval: 5s
      timeout: 10s
      start_period: 10s
      retries: 5
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/localstack:/var/lib/localstack
    env_file:
      - ../settings/.env.${RUN_ENV:-local}

  azurite:
    container_name: azurite
    restart: on-failure
    command: azurite --blobHost 0.0.0.0 --blobPort 10000
    image: mcr.microsoft.com/azure-storage/azurite:3.34.0
    ports:
      - "10000:10000"
    env_file:
      - ../settings/.env.${RUN_ENV:-local}
    networks:
      - stack
    volumes:
      - storagedata:/data

  db01:
    restart: on-failure
    image: postgres:17.4
    container_name: db01
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - stack
    env_file:
      - ../settings/.env.${RUN_ENV:-local}

  pgadmin:
    restart: on-failure
    container_name: pgadmin
    image: dpage/pgadmin4:9.3
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=random
    volumes:
      - pgadmindata:/var/lib/pgadmin
    networks:
      - stack
    depends_on:
      - db01
    ports:
      - "8088:80"

volumes:
  data01:
    driver: local
  pgdata:
    driver: local
  pgadmindata:
    driver: local
  localstack:
    driver: local
  storagedata:
    driver: local

networks:
  stack:
    name: stack
    driver: bridge
