import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    AnalysisCertainty,
    AnalysisRelationshipLabel,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_service import (
    CorrelationService,
    CorrelationServiceInput,
)


class TestCorrelationService:
    @pytest.mark.parametrize(
        "input_data, expected_output",
        [
            (
                # Linear Progression with Noise
                CorrelationServiceInput(
                    data=[
                        (0.0, [2.8, 3.1, 3.3, 2.9, 3.2]),
                        (1.0, [5.2, 4.7, 5.1, 4.9, 5.3]),
                        (2.0, [7.4, 6.8, 7.1, 7.5, 6.9]),
                        (3.0, [9.3, 8.7, 9.1, 9.5, 8.9]),
                        (4.0, [11.2, 10.6, 11.0, 11.4, 10.8]),
                        (5.0, [13.1, 12.5, 12.9, 13.3, 12.7]),
                        (6.0, [15.0, 14.4, 14.8, 15.2, 14.6]),
                        (7.0, [16.9, 16.3, 16.7, 17.1, 16.5]),
                        (8.0, [18.8, 18.2, 18.6, 19.0, 18.4]),
                        (9.0, [20.7, 20.1, 20.5, 20.9, 20.3]),
                        (10.0, [22.6, 22.0, 22.4, 22.8, 22.2]),
                        (11.0, [24.5, 23.9, 24.3, 24.7, 24.1]),
                        (12.0, [26.4, 25.8, 26.2, 26.6, 26.0]),
                        (13.0, [28.3, 27.7, 28.1, 28.5, 27.9]),
                        (14.0, [30.2, 29.6, 30.0, 30.4, 29.8]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Sinusoidal Waveform
                CorrelationServiceInput(
                    data=[
                        (0.0, [0.1, 0.3, -0.2, 0.4, -0.1]),
                        (1.0, [3.2, 3.5, 2.9, 3.8, 3.1]),
                        (2.0, [-0.5, -0.3, -0.7, 0.1, -0.4]),
                        (3.0, [-3.1, -3.4, -2.9, -3.6, -3.0]),
                        (4.0, [0.8, 0.5, 1.1, 0.3, 0.9]),
                        (5.0, [3.3, 3.6, 3.0, 3.9, 3.2]),
                        (6.0, [-0.2, 0.0, -0.4, 0.3, -0.1]),
                        (7.0, [-3.4, -3.7, -3.2, -3.9, -3.3]),
                        (8.0, [0.6, 0.3, 0.9, 0.1, 0.7]),
                        (9.0, [3.5, 3.8, 3.2, 4.1, 3.4]),
                        (10.0, [0.0, 0.2, -0.3, 0.5, -0.1]),
                        (11.0, [-3.3, -3.6, -3.1, -3.8, -3.2]),
                        (12.0, [0.7, 0.4, 1.0, 0.2, 0.8]),
                        (13.0, [3.4, 3.7, 3.1, 4.0, 3.3]),
                        (14.0, [-0.3, -0.1, -0.5, 0.2, -0.2]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.INSUFFICIENT_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.NO_RELATIONSHIP,
                },
            ),
            (
                # Categorical Thresholds
                CorrelationServiceInput(
                    data=[
                        (0.0, [4.8, 5.1, 4.9, 5.2, 5.0]),
                        (1.0, [5.1, 4.9, 5.3, 4.8, 5.2]),
                        (2.0, [5.2, 4.7, 5.4, 4.9, 5.1]),
                        (3.0, [5.0, 5.3, 4.8, 5.1, 4.9]),
                        (4.0, [5.1, 4.9, 5.2, 4.7, 5.0]),
                        (5.0, [14.9, 15.3, 14.8, 15.1, 15.2]),
                        (6.0, [15.2, 14.7, 15.4, 14.9, 15.1]),
                        (7.0, [15.0, 15.3, 14.8, 15.1, 14.9]),
                        (8.0, [15.1, 14.9, 15.2, 14.7, 15.0]),
                        (9.0, [14.8, 15.1, 14.7, 15.0, 14.9]),
                        (10.0, [15.2, 14.8, 15.3, 14.9, 15.1]),
                        (11.0, [14.9, 15.2, 14.8, 15.1, 15.0]),
                        (12.0, [15.1, 14.7, 15.3, 14.8, 15.2]),
                        (13.0, [15.0, 15.3, 14.9, 15.2, 15.1]),
                        (14.0, [14.8, 15.1, 14.7, 15.0, 14.9]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Different ranges for each level of discreet variable
                CorrelationServiceInput(
                    data=[
                        (1, [0.2, -0.5, 0.3, -0.1, 0.6]),
                        (1, [0.8, 0.4, -0.2, 0.9, -0.3]),
                        (2, [-3.2, 4.1, -2.5, 3.9, -4.3]),
                        (2, [2.7, -4.5, 3.3, -3.8, 4.0]),
                        (3, [-6.2, -9.1, -5.8, -7.3, -8.5]),
                        (3, [-9.0, -7.5, -8.2, -6.8, -9.4]),
                        (4, [6.5, 7.2, 5.9, 8.1, 6.7]),
                        (4, [9.3, 6.8, 7.4, 8.9, 5.5]),
                        (1, [-0.6, 0.5, -0.2, 0.3, -0.7]),
                        (1, [0.4, -0.9, 0.2, -0.5, 0.6]),
                        (2, [-4.4, 3.8, -2.7, 4.2, -3.5]),
                        (2, [3.1, -3.6, 4.5, -2.9, 3.7]),
                        (3, [-7.1, -6.3, -8.9, -5.7, -9.6]),
                        (3, [-8.4, -9.8, -6.7, -7.9, -5.2]),
                        (4, [5.4, 7.9, 6.1, 8.3, 9.0]),
                        (4, [8.7, 6.5, 9.2, 7.3, 5.8]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Different ranges for each level of discreet variable
                CorrelationServiceInput(
                    data=[
                        (1, [0]),
                        (1, [1]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (0, [1]),
                        (1, [1, 1, 1, 1, 1, 1]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (1, [0]),
                        (1, [0]),
                        (1, [1, 1, 1, 1, 1, 1]),
                        (1, [1, 1, 1, 1, 1, 1]),
                    ],
                    dependent_field_name=False,
                    independent_field_name=False,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
        ],
    )
    def test_analyze_correlation(self, input_data, expected_output):
        result = CorrelationService().analyze_correlation(input_boundary=input_data, spearman_override=False)
        assert result.correlation.relationship == expected_output["expected_relationship"]
        assert result.correlation.certainty == expected_output["expected_certainty"]

    @pytest.mark.parametrize(
        "input_data, expected_output",
        [
            (
                # Linear Progression with Noise
                CorrelationServiceInput(
                    data=[
                        (0.0, [2.8, 3.1, 3.3, 2.9, 3.2]),
                        (1.0, [5.2, 4.7, 5.1, 4.9, 5.3]),
                        (2.0, [7.4, 6.8, 7.1, 7.5, 6.9]),
                        (3.0, [9.3, 8.7, 9.1, 9.5, 8.9]),
                        (4.0, [11.2, 10.6, 11.0, 11.4, 10.8]),
                        (5.0, [13.1, 12.5, 12.9, 13.3, 12.7]),
                        (6.0, [15.0, 14.4, 14.8, 15.2, 14.6]),
                        (7.0, [16.9, 16.3, 16.7, 17.1, 16.5]),
                        (8.0, [18.8, 18.2, 18.6, 19.0, 18.4]),
                        (9.0, [20.7, 20.1, 20.5, 20.9, 20.3]),
                        (10.0, [22.6, 22.0, 22.4, 22.8, 22.2]),
                        (11.0, [24.5, 23.9, 24.3, 24.7, 24.1]),
                        (12.0, [26.4, 25.8, 26.2, 26.6, 26.0]),
                        (13.0, [28.3, 27.7, 28.1, 28.5, 27.9]),
                        (14.0, [30.2, 29.6, 30.0, 30.4, 29.8]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Sinusoidal Waveform
                CorrelationServiceInput(
                    data=[
                        (0.0, [0.1, 0.3, -0.2, 0.4, -0.1]),
                        (1.0, [3.2, 3.5, 2.9, 3.8, 3.1]),
                        (2.0, [-0.5, -0.3, -0.7, 0.1, -0.4]),
                        (3.0, [-3.1, -3.4, -2.9, -3.6, -3.0]),
                        (4.0, [0.8, 0.5, 1.1, 0.3, 0.9]),
                        (5.0, [3.3, 3.6, 3.0, 3.9, 3.2]),
                        (6.0, [-0.2, 0.0, -0.4, 0.3, -0.1]),
                        (7.0, [-3.4, -3.7, -3.2, -3.9, -3.3]),
                        (8.0, [0.6, 0.3, 0.9, 0.1, 0.7]),
                        (9.0, [3.5, 3.8, 3.2, 4.1, 3.4]),
                        (10.0, [0.0, 0.2, -0.3, 0.5, -0.1]),
                        (11.0, [-3.3, -3.6, -3.1, -3.8, -3.2]),
                        (12.0, [0.7, 0.4, 1.0, 0.2, 0.8]),
                        (13.0, [3.4, 3.7, 3.1, 4.0, 3.3]),
                        (14.0, [-0.3, -0.1, -0.5, 0.2, -0.2]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.INSUFFICIENT_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.NO_RELATIONSHIP,
                },
            ),
            (
                # Categorical Thresholds
                CorrelationServiceInput(
                    data=[
                        (0.0, [4.8, 5.1, 4.9, 5.2, 5.0]),
                        (1.0, [5.1, 4.9, 5.3, 4.8, 5.2]),
                        (2.0, [5.2, 4.7, 5.4, 4.9, 5.1]),
                        (3.0, [5.0, 5.3, 4.8, 5.1, 4.9]),
                        (4.0, [5.1, 4.9, 5.2, 4.7, 5.0]),
                        (5.0, [14.9, 15.3, 14.8, 15.1, 15.2]),
                        (6.0, [15.2, 14.7, 15.4, 14.9, 15.1]),
                        (7.0, [15.0, 15.3, 14.8, 15.1, 14.9]),
                        (8.0, [15.1, 14.9, 15.2, 14.7, 15.0]),
                        (9.0, [14.8, 15.1, 14.7, 15.0, 14.9]),
                        (10.0, [15.2, 14.8, 15.3, 14.9, 15.1]),
                        (11.0, [14.9, 15.2, 14.8, 15.1, 15.0]),
                        (12.0, [15.1, 14.7, 15.3, 14.8, 15.2]),
                        (13.0, [15.0, 15.3, 14.9, 15.2, 15.1]),
                        (14.0, [14.8, 15.1, 14.7, 15.0, 14.9]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Different ranges for each level of discreet variable
                CorrelationServiceInput(
                    data=[
                        (1, [0.2, -0.5, 0.3, -0.1, 0.6]),
                        (1, [0.8, 0.4, -0.2, 0.9, -0.3]),
                        (2, [-3.2, 4.1, -2.5, 3.9, -4.3]),
                        (2, [2.7, -4.5, 3.3, -3.8, 4.0]),
                        (3, [-6.2, -9.1, -5.8, -7.3, -8.5]),
                        (3, [-9.0, -7.5, -8.2, -6.8, -9.4]),
                        (4, [6.5, 7.2, 5.9, 8.1, 6.7]),
                        (4, [9.3, 6.8, 7.4, 8.9, 5.5]),
                        (1, [-0.6, 0.5, -0.2, 0.3, -0.7]),
                        (1, [0.4, -0.9, 0.2, -0.5, 0.6]),
                        (2, [-4.4, 3.8, -2.7, 4.2, -3.5]),
                        (2, [3.1, -3.6, 4.5, -2.9, 3.7]),
                        (3, [-7.1, -6.3, -8.9, -5.7, -9.6]),
                        (3, [-8.4, -9.8, -6.7, -7.9, -5.2]),
                        (4, [5.4, 7.9, 6.1, 8.3, 9.0]),
                        (4, [8.7, 6.5, 9.2, 7.3, 5.8]),
                    ],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
            (
                # Different ranges for each level of discreet variable
                CorrelationServiceInput(
                    data=[
                        (1, [0]),
                        (1, [1]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (0, [1]),
                        (1, [1, 1, 1, 1, 1, 1]),
                        (0, [0]),
                        (0, [0]),
                        (0, [0]),
                        (1, [0]),
                        (1, [0]),
                        (1, [1, 1, 1, 1, 1, 1]),
                        (1, [1, 1, 1, 1, 1, 1]),
                    ],
                    dependent_field_name=False,
                    independent_field_name=False,
                ),
                {
                    "expected_certainty": AnalysisCertainty.STRONG_EVIDENCE,
                    "expected_relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
                },
            ),
        ],
    )
    def test_analyze_correlation_override_spearman_passes(self, input_data, expected_output):
        result = CorrelationService().analyze_correlation(input_boundary=input_data, spearman_override=True)
        assert result.correlation.relationship == expected_output["expected_relationship"]
        assert result.correlation.certainty == expected_output["expected_certainty"]

    @pytest.mark.parametrize(
        "input_data, expected_output",
        [
            (
                CorrelationServiceInput(
                    data=[(1.0, [2.0, 3.0]), (4.0, [5.0, 6.0])],
                    dependent_field_name=False,
                    independent_field_name=True,
                ),
                pd.DataFrame({"dependent": [1.0, 1.0, 4.0, 4.0], "independent": [2.0, 3.0, 5.0, 6.0]}),
            ),
            (
                CorrelationServiceInput(data=[(1.0, [0.0])], dependent_field_name=True, independent_field_name=True),
                pd.DataFrame({"dependent": [1.0], "independent": [0.0]}),
            ),
            (
                CorrelationServiceInput(
                    data=[(1.0, [2.0, 3.0, 4.0]), (5.0, [6.0, 7.0])],
                    dependent_field_name=True,
                    independent_field_name=True,
                ),
                pd.DataFrame({"dependent": [1.0, 1.0, 1.0, 5.0, 5.0], "independent": [2.0, 3.0, 4.0, 6.0, 7.0]}),
            ),
        ],
    )
    def test_process_input_data_into_dataframe(self, input_data, expected_output):
        result = CorrelationService.process_input_data_into_dataframe(input_data)
        result_df = result
        assert_frame_equal(result_df, expected_output)

    @pytest.mark.parametrize(
        "p_value, expected_certainty",
        [
            (0.11, AnalysisCertainty.INSUFFICIENT_EVIDENCE),
            (0.5, AnalysisCertainty.INSUFFICIENT_EVIDENCE),
            (0.10, AnalysisCertainty.WEAK_EVIDENCE),
            (0.07, AnalysisCertainty.WEAK_EVIDENCE),
            (0.05, AnalysisCertainty.MODERATE_EVIDENCE),
            (0.03, AnalysisCertainty.MODERATE_EVIDENCE),
            (0.00, AnalysisCertainty.STRONG_EVIDENCE),
        ],
    )
    def test_get_certainty(self, p_value, expected_certainty):
        result = CorrelationService.get_certainty(p_value=p_value)
        assert result == expected_certainty, f"Expected {expected_certainty} for p-value {p_value}, but got {result}"

    def test_get_certainty_invalid_input(self):
        with pytest.raises(ValueError):
            CorrelationService.get_certainty(-0.1)

        with pytest.raises(ValueError):
            CorrelationService.get_certainty(1.1)

    def test_edge_cases(self):
        empty_df = pd.DataFrame(columns=["dependent", "independent"])
        with pytest.raises(ValueError):
            CorrelationService.calculate_con_to_con(df=empty_df)


@pytest.mark.parametrize(
    "strength_label,p_value,significance_threshold,expected_result",
    [
        (AnalysisRelationshipLabel.WEAK_RELATIONSHIP, 0.06, 0.05, AnalysisRelationshipLabel.NO_RELATIONSHIP),
        (AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP, 0.1, 0.05, AnalysisRelationshipLabel.NO_RELATIONSHIP),
        (AnalysisRelationshipLabel.MODERATE_RELATIONSHIP, 0.06, 0.05, AnalysisRelationshipLabel.POTENTIAL_RELATIONSHIP),
        (AnalysisRelationshipLabel.STRONG_RELATIONSHIP, 0.06, 0.05, AnalysisRelationshipLabel.POTENTIAL_RELATIONSHIP),
        (AnalysisRelationshipLabel.WEAK_RELATIONSHIP, 0.05, 0.05, AnalysisRelationshipLabel.WEAK_RELATIONSHIP),
        (
            AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP,
            0.04,
            0.05,
            AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP,
        ),
        (AnalysisRelationshipLabel.MODERATE_RELATIONSHIP, 0.01, 0.05, AnalysisRelationshipLabel.MODERATE_RELATIONSHIP),
        (AnalysisRelationshipLabel.STRONG_RELATIONSHIP, 0.001, 0.05, AnalysisRelationshipLabel.STRONG_RELATIONSHIP),
    ],
)
def test_sanitize_relationship(strength_label, p_value, significance_threshold, expected_result):
    # Act
    result = CorrelationService.sanitize_relationship(
        strength_label=strength_label, p_value=p_value, significance_threshold=significance_threshold
    )

    # Assert
    assert result == expected_result
