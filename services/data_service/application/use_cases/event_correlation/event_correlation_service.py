import logging
from typing import Literal, Sequence

import numpy as np
import pandas as pd
from pandas import DataFrame
from scipy.stats import chi2_contingency, stats

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    AnalysisCertainty,
    AnalysisRelationshipLabel,
    EventCorrelationAggregate,
)


class CorrelationServiceInput(BaseDataModel):
    data: Sequence[tuple[float, Sequence[float]]]
    dependent_field_name: bool
    independent_field_name: bool


class CorrelationServiceOutput(BaseDataModel):
    correlation: EventCorrelationAggregate
    data: Sequence[tuple[float, float]]
    dependent_type: Literal["occurrence", "discrete", "continuous"]
    independent_type: Literal["occurrence", "discrete", "continuous"]


class CorrelationService:
    """
    Provides static methods for performing correlation analysis between two variables.

    Handles different combinations of variable types:
    - Continuous vs. Continuous (Pearson correlation)
    - Continuous vs. Discrete/Binary (ANOVA F-statistic)
    - Discrete/Binary vs. Discrete/Binary (Chi-squared)
    """




    @staticmethod
    def analyze_correlation(
        input_boundary: CorrelationServiceInput,
        spearman_override: bool,
    ) -> CorrelationServiceOutput:
        logging.info(
            f"Starting correlation analysis for {input_boundary.dependent_field_name} vs. {input_boundary.independent_field_name}..."
        )
        df = CorrelationService.process_input_data_into_dataframe(input_data=input_boundary)
        independent_unique = len(df["independent"].unique())
        dependent_unique = len(df["dependent"].unique())

        dependent_type = CorrelationService._determine_variable_type(
            unique_values_count=dependent_unique, has_field_name=input_boundary.dependent_field_name
        )
        independent_type = CorrelationService._determine_variable_type(
            unique_values_count=independent_unique, has_field_name=input_boundary.independent_field_name
        )

        if independent_unique <= 1 or dependent_unique <= 1:
            return CorrelationServiceOutput(
                data=list(zip(df["dependent"], df["independent"])),
                correlation=EventCorrelationAggregate(
                    correlation_coefficient=0,
                    p_value=1,
                    certainty=AnalysisCertainty.INSUFFICIENT_EVIDENCE,
                    relationship=AnalysisRelationshipLabel.NO_RELATIONSHIP,
                ),
                dependent_type=dependent_type,
                independent_type=independent_type,
            )

        variable_combination = [dependent_type, independent_type]
        if spearman_override:
            corr_res = CorrelationService.calculate_spearman(df=df)
        elif variable_combination == ["continuous", "continuous"]:
            corr_res = CorrelationService.calculate_con_to_con(df=df)
        elif "continuous" in variable_combination:
            corr_res = CorrelationService.calculate_con_to_dis(df=df, dependent_type=dependent_type)
        elif variable_combination in (
            ["occurrence", "occurrence"],
            ["occurrence", "discrete"],
            ["discrete", "occurrence"],
            ["discrete", "discrete"],
        ):
            corr_res = CorrelationService.calculate_dis_to_dis(df=df)
        else:
            raise ShouldNotReachHereException(f"Unsupported variable type combination: {variable_combination}")

        return CorrelationServiceOutput(
            data=list(zip(df["dependent"], df["independent"])),
            correlation=corr_res,
            dependent_type=dependent_type,
            independent_type=independent_type,
        )

    @staticmethod
    def calculate_con_to_con(df: DataFrame) -> EventCorrelationAggregate:
        correlation, p_value = stats.pearsonr(df["dependent"], df["independent"])  # pyright: ignore
        certainty = CorrelationService.get_certainty(p_value=p_value)

        corr_strength = abs(correlation)
        if corr_strength > 0.7:
            relationship = AnalysisRelationshipLabel.STRONG_RELATIONSHIP
        elif corr_strength > 0.5:
            relationship = AnalysisRelationshipLabel.MODERATE_RELATIONSHIP
        elif corr_strength > 0.3:
            relationship = AnalysisRelationshipLabel.WEAK_RELATIONSHIP
        else:
            relationship = AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP

        relationship = CorrelationService.sanitize_relationship(p_value=p_value, strength_label=relationship)

        covariance = df["dependent"].cov(df["independent"])  # pyright: ignore

        # TODO Not sure if we want to return these
        # try:
        #     _, _ = np.polyfit(df["independent"], df["dependent"], 1)  # slope, intercept
        # except np.linalg.LinAlgError:
        #     logging.info("SVD did not converge in Linear Least Squares")

        return EventCorrelationAggregate(
            correlation_coefficient=correlation,
            p_value=p_value,
            covariance=covariance,  # pyright: ignore
            certainty=certainty,
            relationship=relationship,
        )
    @staticmethod
    def calculate_spearman(df: DataFrame) -> EventCorrelationAggregate:
        """Calculate Spearman rank correlation coefficient and p-value."""
        correlation, p_value = stats.spearmanr(df["dependent"], df["independent"])  # pyright: ignore
        certainty = CorrelationService.get_certainty(p_value=p_value)

        corr_strength = abs(correlation)
        if corr_strength > 0.7:
            relationship = AnalysisRelationshipLabel.STRONG_RELATIONSHIP
        elif corr_strength > 0.5:
            relationship = AnalysisRelationshipLabel.MODERATE_RELATIONSHIP
        elif corr_strength > 0.3:
            relationship = AnalysisRelationshipLabel.WEAK_RELATIONSHIP
        else:
            relationship = AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP

        relationship = CorrelationService.sanitize_relationship(p_value=p_value, strength_label=relationship)

        # Calculate covariance of ranks for consistency with other methods
        rank_dependent = df["dependent"].rank()
        rank_independent = df["independent"].rank()
        covariance = rank_dependent.cov(rank_independent)  # pyright: ignore

        return EventCorrelationAggregate(
            correlation_coefficient=correlation,
            p_value=p_value,
            covariance=covariance,  # pyright: ignore
            certainty=certainty,
            relationship=relationship,
        )
    @staticmethod
    def calculate_con_to_dis(
        df: DataFrame, dependent_type: Literal["occurrence", "discrete", "continuous"]
    ) -> EventCorrelationAggregate:
        discrete_series = "dependent" if dependent_type in ["discrete", "occurrence"] else "independent"
        continuous_series = "independent" if discrete_series == "dependent" else "dependent"
        groups = [df[df[discrete_series] == cat][continuous_series] for cat in df[discrete_series].unique()]

        f_statistic, p_value = stats.f_oneway(*groups)  # pyright: ignore

        grand_mean = df[continuous_series].mean()  # pyright: ignore
        ss_total = sum((x - grand_mean) ** 2 for x in df[continuous_series])
        ss_between = sum(len(g) * (np.mean(g) - grand_mean) ** 2 for g in groups)
        eta_squared = ss_between / ss_total

        certainty = CorrelationService.get_certainty(p_value=p_value)
        if eta_squared > 0.14:
            relationship = AnalysisRelationshipLabel.STRONG_RELATIONSHIP
        elif eta_squared > 0.06:
            relationship = AnalysisRelationshipLabel.MODERATE_RELATIONSHIP
        elif eta_squared > 0.01:
            relationship = AnalysisRelationshipLabel.WEAK_RELATIONSHIP
        else:
            relationship = AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP

        relationship = CorrelationService.sanitize_relationship(p_value=p_value, strength_label=relationship)

        return EventCorrelationAggregate(
            correlation_coefficient=eta_squared,  # pyright: ignore
            p_value=p_value,
            certainty=certainty,
            relationship=relationship,
        )

    @staticmethod
    def calculate_dis_to_dis(df: DataFrame) -> EventCorrelationAggregate:
        """Calculate chi-squared and p-value for the contingency table."""
        contingency_table = pd.crosstab(df["dependent"], df["independent"], dropna=False)
        chi2, p_value, dof, _ = chi2_contingency(contingency_table)

        # normalise
        n = contingency_table.sum().sum()
        correlation = np.sqrt(chi2 / (n * (min(contingency_table.shape) - 1)))  # cramers_v

        joint_probs = contingency_table / n
        _ = -np.sum(joint_probs * np.log(joint_probs + 1e-12))  # entropy

        certainty = CorrelationService.get_certainty(p_value=p_value)  # pyright: ignore

        if correlation > 0.35:
            relationship = AnalysisRelationshipLabel.STRONG_RELATIONSHIP
        elif correlation > 0.21:
            relationship = AnalysisRelationshipLabel.MODERATE_RELATIONSHIP
        elif correlation > 0.10:
            relationship = AnalysisRelationshipLabel.WEAK_RELATIONSHIP
        else:
            relationship = AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP

        relationship = CorrelationService.sanitize_relationship(
            p_value=p_value, strength_label=relationship  # pyright: ignore
        )

        return EventCorrelationAggregate(
            correlation_coefficient=correlation,  # pyright: ignore
            p_value=p_value,  # pyright: ignore
            degree_of_freedom=dof,  # pyright: ignore
            certainty=certainty,
            relationship=relationship,
        )

    @staticmethod
    def process_input_data_into_dataframe(
        input_data: CorrelationServiceInput,
    ) -> DataFrame:
        df = (
            DataFrame(input_data.data, columns=["dependent", "independent"])  # pyright: ignore
            .explode("independent")
            .astype({"dependent": float, "independent": float})
        )
        if not input_data.independent_field_name:
            df = df.fillna(0)  # fill NaN rows with 0 for exists
        else:
            df = df.dropna()  # Remove NaN rows for discrete and continuous
        df = df.reset_index(drop=True)
        return df

    @staticmethod
    def get_certainty(p_value: float) -> AnalysisCertainty:
        """Determine level of statistical certainty based on p-value."""
        if p_value < 0 or p_value > 1:
            raise ValueError("p_value must be between 0 and 1")

        if p_value <= 0.01:
            return AnalysisCertainty.STRONG_EVIDENCE
        elif p_value <= 0.05:
            return AnalysisCertainty.MODERATE_EVIDENCE
        elif p_value <= 0.10:
            return AnalysisCertainty.WEAK_EVIDENCE
        else:
            return AnalysisCertainty.INSUFFICIENT_EVIDENCE

    @staticmethod
    def _determine_variable_type(
        has_field_name: bool, unique_values_count: int
    ) -> Literal["occurrence", "discrete", "continuous"]:
        if not has_field_name:
            return "occurrence"

        if unique_values_count > 12:
            return "continuous"
        else:
            return "discrete"

    @staticmethod
    def sanitize_relationship(
        strength_label: AnalysisRelationshipLabel, p_value: float, significance_threshold: float = 0.05
    ) -> AnalysisRelationshipLabel:
        """Apply statistical significance to determine final relationship label."""
        if p_value > significance_threshold:
            if strength_label in [
                AnalysisRelationshipLabel.WEAK_RELATIONSHIP,
                AnalysisRelationshipLabel.VERY_WEAK_RELATIONSHIP,
            ]:
                return AnalysisRelationshipLabel.NO_RELATIONSHIP
            else:
                # For stronger but non-significant relationships
                return AnalysisRelationshipLabel.POTENTIAL_RELATIONSHIP

        # If significant, keep the original strength label
        return strength_label
